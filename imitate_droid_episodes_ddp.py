import json
import pickle
import argparse
import warnings

from torch.utils.tensorboard import SummaryWriter

from tqdm import tqdm
from aloha_scripts.constants import TASK_CONFIGS


from utils.utils_ddp import load_data, set_seed  # data functions
# from utils.lerobot_utils_ddp import load_data, set_seed
from policy_model.droid_dit import DroidDiTPolicy
import torch
import os
from torch.optim.lr_scheduler import CosineAnnealingLR
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist
import torch.multiprocessing as mp

os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['DEVICE'] = "cuda"
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '1'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Suppress specific future warnings from a library
warnings.filterwarnings('ignore', category=FutureWarning)


def get_auto_index(dataset_dir):
    max_idx = 1000
    for i in range(max_idx + 1):
        if not os.path.isfile(os.path.join(dataset_dir, f'qpos_{i}.npy')):
            return i
    raise Exception(f"Error getting auto index, or more than {max_idx} episodes")


def setup(rank, world_size, ddp_port):
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = ddp_port
    torch.cuda.set_device(rank)
    dist.init_process_group("nccl", rank=rank, world_size=world_size)


def train(rank, world_size, args):
    if torch.cuda.device_count() > 1:
        setup(rank, world_size, args['ddp_port'])

    set_seed(args['seed'])
    # command line parameters
    ckpt_dir = args['ckpt_dir']
    policy_class = args['policy_class']
    task_name = args['task_name']
    batch_size_train = args['batch_size']
    num_steps = args['num_steps']
    save_every = args['save_every']
    resume = args['resume']
    backbone = args['backbone']

    # for diffusion
    img_fea_dim = args['img_fea_dim']
    cond_obs_dim = args['cond_obs_dim']
    use_color_rand = args['use_color_rand']

    task_config = TASK_CONFIGS[task_name]
    dataset_dir = task_config['dataset_dir']
    episode_len = task_config['episode_len']
    camera_names = task_config['camera_names']
    name_filter = task_config.get('name_filter', lambda n: True)
    stats_dir = task_config.get('stats_dir', None)

    # double arm for aloha, and single arm for franka
    if args['mobile_aloha']:
        assert args['double_arm'] != 1
        state_dim = 20  # 14
        action_dim = 20
    elif args['double_arm']:
        state_dim = 14  # 14
        action_dim = 14
    else:
        state_dim = 7  # 14
        action_dim = 10


    # init policy config
    policy_config = {'lr': args['lr'],
                     'camera_names': camera_names,
                     'state_dim': state_dim,
                     'action_dim': action_dim,
                     'observation_horizon': 1,
                     'prediction_horizon': args['chunk_size'],
                     'num_queries': args['chunk_size'],
                     'num_inference_timesteps': 10,
                     'ema_power': 0.75,
                     'backbone': backbone,
                     'weight_decay': 0.0,
                     'img_fea_dim': img_fea_dim,
                     'cond_obs_dim': cond_obs_dim,
                     'num_noise_samples': args['num_noise_samples'],
                     'use_color_rand': use_color_rand,
                     'image_size_w': args['image_size_w'],  # type=int,default=640)
                     'image_size_h': args['image_size_h'],  # type=int,default=480)
                     'lr_backbone':args['lr_backbone'],
                     "using_vit_film": args['using_vit_film'],
                     }
    if policy_class == 'DroidDiffusion':
        pass  # do not add other configs
    elif policy_class == 'DroidDiT':
        # have aug data in dataset!
        use_color_rand = False
        policy_config["model_size"] = args['model_size']
        policy_config["use_color_rand"] = use_color_rand
    else:
        raise NotImplementedError

    config = {
        'num_steps': num_steps,
        'save_every': save_every,
        'ckpt_dir': ckpt_dir,
        'resume': resume,
        'episode_len': episode_len,
        'state_dim': state_dim,
        'lr': args['lr'],
        'policy_class': policy_class,
        'policy_config': policy_config,
        'task_name': task_name,
        'seed': args['seed'],
        'camera_names': camera_names,
        'action_dim': action_dim,
        'load_pretrain': args['load_pretrain']
    }

    if not os.path.isdir(ckpt_dir):
        os.makedirs(ckpt_dir, exist_ok=True)
    config_path = os.path.join(ckpt_dir, 'config.json')

    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)

    train_dataloader, stats = load_data(
        dataset_dir_l=dataset_dir,
        camera_names=camera_names,
        batch_size_train=batch_size_train,
        chunk_size=args['chunk_size'],
        stats_dir_l=stats_dir,
        args=args,
        rank=rank)

    print(">>>>>>>>>>>>>start training>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    train_bc(train_dataloader, config, rank,args=args)


def make_policy(policy_class, policy_config, rank=None):
    if policy_class == 'DroidDiT':
        policy = DroidDiTPolicy(policy_config)
    else:
        raise NotImplementedError
    return policy


def make_optimizer(policy_class, policy):
    if policy_class == 'DroidDiffusion':
        if torch.cuda.device_count() > 1:
            optimizer = policy.module.configure_optimizers()
        else:
            optimizer = policy.configure_optimizers()
    elif policy_class == 'DroidDiT':
        if torch.cuda.device_count() > 1:
            optimizer = policy.module.configure_optimizers()
        else:
            optimizer = policy.configure_optimizers()
    else:
        raise NotImplementedError
    return optimizer


def forward_pass(data, policy,step=0,rank=0):
    image_data, qpos_data, action_data, is_pad, lang_embed = data
    image_data, qpos_data, action_data, is_pad, = image_data.cuda().to(torch.float32), qpos_data.cuda().to(torch.float32), action_data.cuda().to(torch.float32), is_pad.cuda()
    if lang_embed is not None:
        lang_embed = lang_embed.cuda()
        lang_embed = lang_embed.to(torch.float32)

    return policy(qpos_data, image_data, action_data, is_pad, language_distilbert=lang_embed)  # TODO remove None


def train_bc(train_dataloader, config, rank, args=None):
    num_steps = config['num_steps']
    ckpt_dir = config['ckpt_dir']
    policy_class = config['policy_class']
    policy_config = config['policy_config']
    save_every = config['save_every']
    writer = SummaryWriter(log_dir=os.path.join(ckpt_dir,"tensorboard_logs"))

    curr_step = 0
    policy = make_policy(policy_class, policy_config, rank=rank)
    if rank is not None and rank == 0:
        print(policy)
    if torch.cuda.device_count() > 1:
        policy = policy.to(rank)
        policy = DDP(policy, device_ids=[rank], find_unused_parameters=True)
    else:
        policy.cuda()

    optimizer = make_optimizer(policy_class, policy)
    print("eta_min: ", args["eta_min"])
    scheduler = CosineAnnealingLR(optimizer, T_max=num_steps, eta_min=args["eta_min"])

    if config['load_pretrain']:
        ckpt_filepath = args['load_pre_path']
        # loading_status = policy.deserialize(torch.load(ckpt_filepath), replace_pool=True)
        checkpoint = torch.load(ckpt_filepath,map_location=torch.device('cpu'))
        status = policy.deserialize(checkpoint['nets'])
        print(f">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>load pre train ckpt>>>>>>>>>>>>>>>>>>>{ckpt_filepath}")
        if rank == 0:
            print(f'loaded! {status}')

    if config['resume']:
        print(f">>>>>>>>>>>>>>>>>>>>>>>>start resmue Loading model {config['resume']}>>>>>>>>>>>>>>>>>>>>>>>")
        file_list=os.listdir(ckpt_dir)
        for file in file_list:
            if file.endswith(".ckpt") and "compressed" not in file:
                step_number=int(file.split("step_")[1].split("_")[0])
                if step_number>curr_step:
                    curr_step=step_number
                    resume_ckpt_path = os.path.join(ckpt_dir, file)
        print(">>>>>>>>>>>>>>>>max resume step: ", curr_step, ">>>>>>>>>>>>>>>>>>>>>>>")
        checkpoint = torch.load(resume_ckpt_path, map_location=torch.device('cpu'))
        status = policy.deserialize(checkpoint['nets'], steps=checkpoint["step"]+1)
        curr_step = checkpoint["step"] + 1
        optimizer_state = checkpoint["optimizer"]
        scheduler_state = checkpoint["scheduler"]
        loading_status = status

        print(f'curr_step: {curr_step}, num_steps: {num_steps}')
        print(f'Resume policy from: {config["resume"]}, Status: {loading_status}')

        optimizer.load_state_dict(optimizer_state)
        scheduler.load_state_dict(scheduler_state)
        print(f'Resume optimizer from: {config["resume"]}')
        print(f'Resume scheduler from: {config["resume"]}')
        del optimizer_state
        del scheduler_state
        del checkpoint

    train_dataloader = repeater(train_dataloader, num_steps + 2, rank)
    loss = -1.0
    print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>start training>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    for step in tqdm(range(curr_step, num_steps + 1)):
        policy.train()
        optimizer.zero_grad()
        data = next(train_dataloader)
        forward_dict = forward_pass(data, policy, step=step, rank=rank)
        loss = forward_dict['loss'].mean()
        loss.backward()

        #tensorboard log
        if rank == 0:
            if step % 10 == 0:
                writer.add_scalar("Loss/train", loss, global_step=step)
                writer.add_scalar("lr", optimizer.param_groups[0]["lr"], global_step=step)

        optimizer.step()
        if not args["use_constant"]:
            scheduler.step()

        if step == 0 or step % save_every == 0:
            if rank == 0:
                ckpt_path = os.path.join(ckpt_dir, f'policy_step_{step}.ckpt')
                state = policy.module.serialize() if torch.cuda.device_count() > 1 else policy.serialize()
                curr_ckpt_info = {"step": step + 1,
                                  "nets": state,
                                  "optimizer": optimizer.state_dict(),
                                  "scheduler": scheduler.state_dict(),
                                  "loss": loss}
                torch.save(curr_ckpt_info, ckpt_path)


def repeater(data_loader, total_steps, rank=0):
    step = 0
    while step < total_steps:
        # Create a new iterator for each epoch to ensure proper shuffling and distribution
        iterator = iter(data_loader)
        for data in iterator:
            yield data
            step += 1
            if step >= total_steps:
                break

        # Since the DataLoader is exhausted, synchronize all processes here
        if torch.distributed.is_initialized():
            torch.distributed.barrier()

        # Optionally, log the completion of an epoch on rank 0
        if rank == 0:
            print(f"Completed full pass through the DataLoader at step {step}")


def main(args):
    world_size = torch.cuda.device_count()
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    ### training hyperparameters
    parser.add_argument('--ckpt_dir', action='store', type=str, help='ckpt dir used for saving traing ckpts', required=True)
    parser.add_argument('--policy_class', action='store', type=str, help='the policy class used for imitation learning, DroidDiT, capitalize', required=True)
    parser.add_argument('--task_name', action='store', type=str, help='task_name', required=True)
    parser.add_argument('--batch_size', action='store', type=int, help='batch_size', required=True)
    parser.add_argument('--seed', action='store', type=int, help='seed', required=True)
    parser.add_argument('--num_steps', action='store', type=int, help='number of training steps', required=True)
    parser.add_argument('--lr', action='store', type=float, help='lr', required=True)
    parser.add_argument('--lr_backbone', default=1e-4, type=float, help='lr_backbone')
    parser.add_argument('--eta_min', default=0.0, type=float, help='eta_min')
    parser.add_argument('--save_every', action='store', type=int, default=500, help='save_every', required=False)

    ### model hyperparameters
    parser.add_argument('--num_noise_samples', type=int, default=8)
    parser.add_argument('--chunk_size', action='store', type=int, help='chunk_size', required=False)
    parser.add_argument('--backbone', type=str, default='resnet18')
    parser.add_argument('--img_fea_dim', type=int, default=512)
    parser.add_argument('--cond_obs_dim', type=int, default=512)
    parser.add_argument('--model_size', default="DiT-S", type=str)
    parser.add_argument('--using_vit_film', action='store_true', default=False)

    ### data hyperparameters
    parser.add_argument('--image_size_w', type=int, default=640)
    parser.add_argument('--image_size_h', type=int, default=480)
    parser.add_argument('--bgr', type=int, default=0)
    parser.add_argument('--use_color_rand', action='store_true', default=False)

    ### state hyperparameters (input dim and output dim setup)
    parser.add_argument('--double_arm', type=int, default=0)
    parser.add_argument('--mobile_aloha', type=int, default=0)
    parser.add_argument('--is_use_sub_reason', default=0, type=int)

    ### pretrain and resume hyperparameters
    parser.add_argument('--load_pretrain', action='store_true', default=False)
    parser.add_argument('--resume', type=int, help='resume train', default=0)
    parser.add_argument('--load_pre_path', type=str, help='load pretrain model path', default=None)
    parser.add_argument("--use_constant", type=int, help='for optimizer shelduer', default=0)

    ### ddp setup
    parser.add_argument('--ddp_port', action='store', type=str, default='12355')
    main(vars(parser.parse_args()))
