# copy of llava
import torch
import torch.nn as nn

from transformers import CLIPVisionModel

class CLIPVisionTower(nn.Module):
    def __init__(self, vision_tower, camera_names):
        super().__init__()
        self.vision_tower_name = vision_tower
        self.camera_names = camera_names
        self.select_layer = -2
        self.select_feature = 'cls'
        self.load_model()

    def load_model(self, device_map=None):
        if "clip" in self.vision_tower_name:
            self.vision_tower = CLIPVisionModel.from_pretrained(self.vision_tower_name, device_map=device_map)
        else:
            raise NotImplementedError

    def feature_select(self, image_forward_outs):
        image_features = image_forward_outs.hidden_states[self.select_layer]
        if self.select_feature == 'patch':
            image_features = image_features[:, 1:]
        elif self.select_feature == 'cls_patch':
            image_features = image_features
        elif self.select_feature == 'cls':
            image_features = image_features[:, :1]
        else:
            raise ValueError(f'Unexpected select feature: {self.select_feature}')
        return image_features

    def forward(self, images, image_grid_thw=None):
        views = images.shape[1]
        feature_list = []
        for j in range(views):
            image = images[:, j]
            image_forward_out = self.vision_tower(image.to(device=self.device, dtype=self.dtype), output_hidden_states=True)
            image_feature = self.feature_select(image_forward_out).to(images.dtype)
            image_feature = image_feature.mean(dim=1)  # mean along the token dimension
            feature_list.append(image_feature)
        image_features = torch.stack(feature_list, dim=1)
        return image_features

    @property
    def dummy_feature(self):
        return torch.zeros(1, self.hidden_size, device=self.device, dtype=self.dtype)

    @property
    def dtype(self):
        return self.vision_tower.dtype

    @property
    def device(self):
        return self.vision_tower.device

    @property
    def config(self):
        return self.vision_tower.config

    @property
    def hidden_size(self):
        return self.config.hidden_size

    @property
    def num_patches_per_side(self):
        return self.config.image_size // self.config.patch_size

    @property
    def num_patches(self):
        return (self.config.image_size // self.config.patch_size) ** 2