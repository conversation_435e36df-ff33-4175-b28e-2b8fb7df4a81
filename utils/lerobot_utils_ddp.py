import json
import os
import pickle

import cv2
import timm
import torch
import numpy as np
from copy import deepcopy
from PIL import Image
from tqdm import tqdm

cv2.setNumThreads(1)
from qwen_vl_utils import fetch_image
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
import torch.nn.functional as F
from torch.utils.data import DistributedSampler
from transformers import CLIPImageProcessor

from aloha_scripts.constants import TASK_CONFIGS
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset, LeRobotDatasetMetadata

from typing import Protocol, SupportsIndex, TypeVar
T_co = TypeVar("T_co", covariant=True)

def resize_with_pad(img, width, height, pad_value=-1):
    # assume no-op when width height fits already
    if img.ndim != 4:
        raise ValueError(f"(b,c,h,w) expected, but {img.shape}")

    cur_height, cur_width = img.shape[2:]

    ratio = max(cur_width / width, cur_height / height)
    resized_height = int(cur_height / ratio)
    resized_width = int(cur_width / ratio)
    resized_img = F.interpolate(
        img, size=(resized_height, resized_width), mode="bilinear", align_corners=False
    )

    pad_height = max(0, int(height - resized_height))
    pad_width = max(0, int(width - resized_width))

    # pad on left and top of image
    padded_img = F.pad(resized_img, (pad_width, 0, pad_height, 0), value=pad_value)
    return padded_img

class Dataset(Protocol[T_co]):
    """Interface for a dataset with random access."""

    def __getitem__(self, index: SupportsIndex) -> T_co:
        raise NotImplementedError("Subclasses of Dataset should implement __getitem__.")

    def __len__(self) -> int:
        raise NotImplementedError("Subclasses of Dataset should implement __len__.")


class TransformedDataset(Dataset[T_co]):
    def __init__(self, dataset: Dataset ,norm_stats,camera_names,args=None, rank=None):
        self._dataset = dataset
        self.norm_stats = norm_stats
        self.bgr = args['bgr']
        self.is_use_sub_reason = args['is_use_sub_reason']
        self.use_base = args['use_base']
        self.args = args
        self.camera_names = camera_names
        try:
            with open(os.path.join("/media/jz08/Elements/ljm/data", dataset.repo_id,'distilbert.pkl'), 'rb') as f:
                distilbert_dict = pickle.load(f)
        except:
            with open(os.path.join("/home/<USER>/tzb/lerobot_data/aloha", dataset.repo_id,'distilbert.pkl'), 'rb') as f:
                distilbert_dict = pickle.load(f)
        self.distilbert_dict = distilbert_dict # all use distilbert_dict include sub_reason
        if rank is None and rank == 0:
            print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>load distilbert dict done>>>>>>>>>>>>>>>>>>")

        self.policy_class = args['policy_class']
        self.backbone = args['backbone']

        self.lang_embed = torch.zeros(768)
        if "clip" in self.backbone:
            self.image_processor = CLIPImageProcessor.from_pretrained(self.backbone)
        elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone:
            with open(os.path.join(self.backbone, "data_config.json"), 'r', encoding='utf-8') as f:
                data_config = json.load(f)
            self.image_processor = timm.data.create_transform(**data_config, is_training=False)
        else:
            raise NotImplementedError

        if rank is None and rank == 0:
            print(f"cur dataset augment_images: {self.augment_images}")
            print(f"cur image resize_shape: {self.resize_shape}")

        self.transformations = None
        self.ele = {'resized_height': 224, "resized_width": 224}
        self.__getitem__(0)

    def __getitem__(self, index: SupportsIndex) -> T_co:
        data = self._dataset[index]
        is_pad = data['action_is_pad']
        # sub_reason = data.meta.

        if self.args['is_use_sub_reason']:
            sub_reason_raw = "language_distilbert"  # default not using reasoning
            for k in ['substep_reasonings', 'reason']:
                vals = self._dataset.meta.episodes[data['episode_index']]["language_dict"][k]
                if vals is not None:
                    if k == 'substep_reasonings':
                        sub_reason_raw = vals[data['frame_index']]
                    else:
                        sub_reason_raw = "language_distilbert"  # default not using reasoning
            try:
                lang_embed = self.distilbert_dict[sub_reason_raw].squeeze()
            except:
                print(self._dataset.repo_id)
                exit()
        else:
            ##### zzy modify here #########
            lang_embed =  self.distilbert_dict['language_distilbert']
            # lang_embed = self.lang_embed
            ###############################
        lang_embed = torch.tensor(lang_embed).float().squeeze()

        all_cam_images = []
        for cam_name in self.camera_names:
            # Check if image is available
            image = data[cam_name].numpy()
            if image.shape[0] != 3:
                image = np.transpose(image, (2, 0, 1))
            image = torch.from_numpy(image)
            if self.transformations is None:
                print('Initializing transformations')
                original_size = image.shape[1:]
                ratio = 0.95
                self.transformations = [
                    transforms.RandomCrop(size=[int(original_size[0] * ratio), int(original_size[1] * ratio)]),
                    transforms.Resize(original_size, antialias=True),
                    transforms.RandomRotation(degrees=[-5.0, 5.0], expand=False),
                    transforms.ColorJitter(brightness=0.3, contrast=0.4, saturation=0.5)  # , hue=0.08)
                ]
            for transform in self.transformations:
                image = transform(image)

            all_cam_images.append(image)

        image_data = torch.stack(all_cam_images, dim=0)
        image_data = resize_with_pad(image_data, 224, 224, 0)
        if "clip" in self.backbone:
            image_data = self.image_processor.preprocess(image_data, return_tensors='pt')['pixel_values'][0]
        elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone:
            image_data = self.image_processor(image_data.float())
        qpos_data = data['observation.state'].float()
        action_data = data['action'].float()

        norm_stats = self.norm_stats

        # normalize action to [-1, 1] for ddpm
        action_data = ((action_data - norm_stats["action_min"]) / (norm_stats["action_max"] - norm_stats["action_min"])) * 2 - 1
        qpos_data = (qpos_data - norm_stats["qpos_mean"]) / norm_stats["qpos_std"]

        return image_data, qpos_data, action_data, is_pad, lang_embed

    def __len__(self) -> int:
        return len(self._dataset)


def get_norm_stats(dataset_list,args):
    """
    caculate all data action and qpos(robot state ) mean and std
    """
    all_qpos_data = []
    mean_list = []
    std_list = []
    length_list = []
    state_min_list = []
    state_max_list = []
    action_mean_list = []
    action_std_list = []
    action_max_list = []
    action_min_list = []

    # Collect data from each dataset
    for dataset in tqdm(dataset_list):
        # Convert the tensor to a numpy array
        mean_tensor = dataset.meta.stats["observation.state"]["mean"]
        std_tensor = dataset.meta.stats["observation.state"]["std"]
        state_max = dataset.meta.stats["observation.state"]["max"]
        state_min = dataset.meta.stats["observation.state"]["min"]

        action_mean = dataset.meta.stats["action"]["mean"]
        action_std = dataset.meta.stats["action"]["std"]
        action_min = dataset.meta.stats["action"]["min"]
        action_max = dataset.meta.stats["action"]["max"]
        # Ensure the tensors are on CPU and convert to numpy arrays
        mean_array = mean_tensor.cpu().numpy() if mean_tensor.is_cuda else mean_tensor.numpy()
        std_array = std_tensor.cpu().numpy() if std_tensor.is_cuda else std_tensor.numpy()
        state_max = state_max.cpu().numpy() if state_max.is_cuda else state_max.numpy()
        state_min = state_min.cpu().numpy() if state_min.is_cuda else state_min.numpy()


        action_mean = action_mean.cpu().numpy() if action_mean.is_cuda else action_mean.numpy()
        action_std = action_std.cpu().numpy() if action_std.is_cuda else action_std.numpy()
        action_min = action_min.cpu().numpy() if action_min.is_cuda else action_min.numpy()
        action_max = action_max.cpu().numpy() if action_max.is_cuda else action_max.numpy()

        # Append the arrays and the length of the dataset (number of samples)
        mean_list.append(mean_array)
        std_list.append(std_array)
        state_max_list.append(state_max)
        state_min_list.append(state_min)
        action_mean_list.append(action_mean)
        action_std_list.append(action_std)
        action_max_list.append(action_max)
        action_min_list.append(action_min)

        length_list.append(len(dataset))  # This is a single number, representing the number of samples

    # Convert lists to numpy arrays for easier manipulation
    mean_array = np.array(mean_list)  # Shape should be (num_datasets, 14)
    std_array = np.array(std_list)  # Shape should be (num_datasets, 14)
    length_array = np.array(length_list)  # Shape should be (num_datasets,)

    action_mean = np.array(action_mean_list)
    action_std = np.array(action_std_list)

    # Now, mean_array has the shape (num_datasets, 14) and length_array has the shape (num_datasets,)

    # To calculate the weighted mean:
    # weighted_mean = sum(mean * length) / total_length
    state_max = np.max(state_max_list, axis=0)
    state_min = np.min(state_min_list, axis=0)
    action_max = np.max(action_max_list, axis=0)
    action_min = np.min(action_min_list, axis=0)

    state_mean = np.sum(mean_array.T * length_array, axis=1) / np.sum(length_array)

    # To calculate the weighted variance (pooled variance):
    # weighted_variance = sum((length - 1) * std^2 + length * (mean - weighted_mean)^2) / total_length
    state_weighted_variance = np.sum(((length_array[:, None] - 1) * std_array ** 2 + (length_array[:, None] - 1) *mean_array**2),axis=0)/np.sum(length_array) - state_mean**2

    # Calculate the overall standard deviation (square root of variance)
    state_std = np.sqrt(state_weighted_variance)

    action_weighted_mean = np.sum(action_mean.T * length_array, axis=1) / np.sum(length_array)
    action_weighted_variance = np.sum(((length_array[:, None] - 1) * action_std ** 2 + (length_array[:, None] - 1) *action_mean**2),axis=0)/np.sum(length_array) - action_weighted_mean**2
    action_weighted_std = np.sqrt(action_weighted_variance)
    # Output the results
    print(f"Overall Weighted Mean: {state_mean}")
    print(f"Overall Weighted Std: {state_std}")

    eps = 0.0001
    stats = {"action_mean": action_weighted_mean, "action_std": action_weighted_std,
             "action_min": action_min - eps, "action_max": action_max + eps,
             "qpos_mean": state_mean, "qpos_std": state_std,
             }
    # with open("stats.pkl", "wb") as f:
    #     pickle.dump(stats, f)
    all_episode_len = np.sum(length_array)
    return stats, all_episode_len


def create_dataset(repo_id,args,camera_names) ->  Dataset:
    home_lerobot = "/home/<USER>/tzb/lerobot_data/aloha/"
    with open(os.path.join(home_lerobot, repo_id, "meta",'info.json'), 'r') as f:
            data= json.load(f)
    fps = data['fps']
    delta_timestamps = {
        # "observation.state": [t / fps for t in range(args['chunk_size'])],
        "action": [t / fps for t in range(args['chunk_size'])],
    }

    dataset = LeRobotDataset(repo_id,  delta_timestamps=delta_timestamps, local_files_only=True)
    return  dataset

def load_data(camera_names, batch_size_train, args=None, rank=None, **kwargs):
    repo_id_list = TASK_CONFIGS[args['task_name']]['dataset_dir']
    dataset_list = []
    for repo_id in tqdm(repo_id_list):
        dataset = create_dataset(repo_id,args,camera_names)
        dataset_list.append(dataset)
    norm_stats, all_episode_len = get_norm_stats(dataset_list,args)
    print("episode len:", all_episode_len)
    print("all task rumber:", len(dataset_list))
    train_dataset_list =[]
    for dataset in dataset_list:
      train_dataset_list.append(TransformedDataset(dataset, norm_stats,camera_names,args=args, rank=rank))
    num_replicas = torch.cuda.device_count()
    train_dataset = torch.utils.data.ConcatDataset(train_dataset_list)
    sampler = DistributedSampler(train_dataset,num_replicas=num_replicas,rank=rank,drop_last=True)
    train_dataloder = DataLoader(train_dataset, batch_size=batch_size_train, sampler=sampler, num_workers=8, pin_memory=True,prefetch_factor=2)

    return train_dataloder, norm_stats


def get_norm_stats_by_tasks(dataset_path_list,args):
    data_tasks_dict = dict(
        fold_shirt=[],
        clean_table=[],
        others=[],
    )
    for dataset_path in dataset_path_list:
        if 'fold' in dataset_path or 'shirt' in dataset_path:
            key = 'fold_shirt'
        elif 'clean_table' in dataset_path and 'pick' not in dataset_path:
            key = 'clean_table'
        else:
            key = 'others'
            base_action = preprocess_base_action(base_action)
        data_tasks_dict[key].append(dataset_path)
    norm_stats_tasks = {k: None for k in data_tasks_dict.keys()}
    for k, v in data_tasks_dict.items():
        if len(v) > 0:
            norm_stats_tasks[k], _ = get_norm_stats(v,args=args)
    return norm_stats_tasks


def calibrate_linear_vel(base_action, c=None):
    if c is None:
        c = 0.0  # 0.19
    v = base_action[..., 0]
    w = base_action[..., 1]
    base_action = base_action.copy()
    base_action[..., 0] = v - c * w
    return base_action


def smooth_base_action(base_action):
    return np.stack([
        np.convolve(base_action[:, i], np.ones(5) / 5, mode='same') for i in range(base_action.shape[1])
    ], axis=-1).astype(np.float32)


def preprocess_base_action(base_action):
    # base_action = calibrate_linear_vel(base_action)
    base_action = smooth_base_action(base_action)

    return base_action


def postprocess_base_action(base_action):
    linear_vel, angular_vel = base_action
    linear_vel *= 1.0
    angular_vel *= 1.0
    # angular_vel = 0
    # if np.abs(linear_vel) < 0.05:
    #     linear_vel = 0
    return np.array([linear_vel, angular_vel])


### env utils
def sample_box_pose():
    x_range = [0.0, 0.2]
    y_range = [0.4, 0.6]
    z_range = [0.05, 0.05]

    ranges = np.vstack([x_range, y_range, z_range])
    cube_position = np.random.uniform(ranges[:, 0], ranges[:, 1])

    cube_quat = np.array([1, 0, 0, 0])
    return np.concatenate([cube_position, cube_quat])


def sample_insertion_pose():
    # Peg
    x_range = [0.1, 0.2]
    y_range = [0.4, 0.6]
    z_range = [0.05, 0.05]

    ranges = np.vstack([x_range, y_range, z_range])
    peg_position = np.random.uniform(ranges[:, 0], ranges[:, 1])

    peg_quat = np.array([1, 0, 0, 0])
    peg_pose = np.concatenate([peg_position, peg_quat])

    # Socket
    x_range = [-0.2, -0.1]
    y_range = [0.4, 0.6]
    z_range = [0.05, 0.05]

    ranges = np.vstack([x_range, y_range, z_range])
    socket_position = np.random.uniform(ranges[:, 0], ranges[:, 1])

    socket_quat = np.array([1, 0, 0, 0])
    socket_pose = np.concatenate([socket_position, socket_quat])

    return peg_pose, socket_pose


### helper functions
def compute_dict_mean(epoch_dicts):
    result = {k: None for k in epoch_dicts[0]}
    num_items = len(epoch_dicts)
    for k in result:
        value_sum = 0
        for epoch_dict in epoch_dicts:
            value_sum += epoch_dict[k]
        result[k] = value_sum / num_items
    return result


def detach_dict(d):
    new_d = dict()
    for k, v in d.items():
        new_d[k] = v.detach()
    return new_d


def set_seed(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)
