import os
import h5py
import cv2
import warnings
import logging
import numpy as np

logging.getLogger("glfw").setLevel(logging.ERROR)  # Ignores warnings and below

# Suppress specific future warnings from a library
warnings.filterwarnings('ignore', category=FutureWarning)

dir_paths = [
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250530_random_fold_stacked_T-shirts_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250603_random_fold_stacked_T-shirts_zby_2_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250603_random_fold_stacked_T-shirts_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250521_fold_pants_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250522_fold_pants_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250523_fold_pants_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250526_fold_pants_lyp_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250526_fold_pants_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250527_fold_pants_lyp_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250527_fold_pants_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250528_fold_T-shirts_zby_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250529_fold_T-shirts_lyp_compressed",
    # "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250529_fold_T-shirts_zby_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250526_random_folding_pants_Leo_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250527_random_folding_pants_Leo_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_Leo_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_zjm_2_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_zjm_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_Leo_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_zjm_2_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_zjm_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250530_random_folding_pants_zjm_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250603_random_folding_pants_lyp_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250603_random_folding_pants_zjm_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_Leo_20250522_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_zjm_20250522_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_zjm_20250523_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_Leo_20250526_noon_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250526_2_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250526_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250527_2_compressed",
    "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250527_compressed"
]


def append_path(file_path, cam_name, start_ts, log_file="/data/private/joy/failed_compressed_image_2.txt"):
    """
    检查一个条件，如果为True，则将file_path追加写入到log_file中。

    Args:
        file_path (str): 需要判断的路径。
        log_file (str): 写入路径的文本文件。如果文件不存在，则创建。
    """

    try:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(str(file_path) + '\t' + str(cam_name) + '\t' + str(start_ts) + '\n')
        print(f"'{file_path}' 满足条件，已追加写入到 '{log_file}' 中。")
    except IOError as e:
        print(f"写入文件时发生错误: {e}")


def check_hdf5_file(dataset_path, camera_names=['cam_high', 'cam_left_wrist', 'cam_right_wrist']):
    with h5py.File(dataset_path, 'r', libver='latest', swmr=True) as root:
        compressed = root.attrs.get('compress', False)
        ### read image and do forward preprocess for it
        for cam_name in camera_names:
            if not root.get(f'/observations/images/{cam_name}', None):
                print("path", dataset_path)
                print("cam_names", cam_name)
                append_path(dataset_path, cam_name, 999)
                continue
            for start_ts, item in enumerate(root[f'/observations/images/{cam_name}']):
                try:
                    image = root[f'/observations/images/{cam_name}'][start_ts]
                    if compressed:
                        image = cv2.imdecode(image, 1)
                    if image.shape[0] != 3:
                        image = np.transpose(image, (2, 0, 1))
                except:
                    print("path", dataset_path)
                    print("cam_names", cam_name)
                    print("start_ts", start_ts)
                    append_path(dataset_path, cam_name, start_ts)

                    continue
                    # print(f'===debug image: {image.shape}')

                    # exit(0)


num = 0
for directory in dir_paths:
    # 遍历指定目录及其子目录中的所有文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            # 检查文件后缀是否为 .hdf5
            if file.endswith('.hdf5'):
                num += 1
                print(num)
                # 构建完整的文件路径
                file_path = os.path.join(root, file)
                # 打印文件路径
                print(file_path)
                check_hdf5_file(file_path)


