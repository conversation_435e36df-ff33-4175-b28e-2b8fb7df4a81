import pickle

import torch
import os
import h5py
import fnmatch
import cv2
import json
import warnings
import logging
import numpy as np

import timm
import torchvision.transforms as transforms
import torch.nn.functional as F

from torch.utils.data import DataLoader, DistributedSampler
from transformers import CLIPImageProcessor


logging.getLogger("glfw").setLevel(logging.ERROR)  # Ignores warnings and below

# Suppress specific future warnings from a library
warnings.filterwarnings('ignore', category=FutureWarning)


def resize_with_pad(img, width, height, pad_value=-1):
    # assume no-op when width height fits already
    if img.ndim != 4:
        raise ValueError(f"(b,c,h,w) expected, but {img.shape}")

    cur_height, cur_width = img.shape[2:]

    ratio = max(cur_width / width, cur_height / height)
    resized_height = int(cur_height / ratio)
    resized_width = int(cur_width / ratio)
    resized_img = F.interpolate(
        img, size=(resized_height, resized_width), mode="bilinear", align_corners=False
    )

    pad_height = max(0, int(height - resized_height))
    pad_width = max(0, int(width - resized_width))

    # pad on left and top of image
    padded_img = F.pad(resized_img, (pad_width, 0, pad_height, 0), value=pad_value)
    return padded_img


class EpisodicDataset(torch.utils.data.Dataset):
    def __init__(self, dataset_path_list, camera_names, norm_stats, episode_ids, episode_len, chunk_size, args=None, rank=None):
        super(EpisodicDataset).__init__()
        self.dataset_path_list = dataset_path_list
        self.camera_names = camera_names
        self.norm_stats = norm_stats
        self.episode_ids = episode_ids
        self.episode_len = episode_len
        self.cumulative_len = np.cumsum(self.episode_len)
        self.chunk_size = chunk_size
        self.bgr = args['bgr']
        if rank is None and rank == 0:
            print(f"total dataset cumulative_len: {self.cumulative_len}")
        else:
            print(f"total dataset cumulative_len: {self.cumulative_len}")
        self.max_episode_len = max(episode_len)
        self.policy_class = args['policy_class']
        self.backbone = args['backbone']

        # augment images for training (default for dp and scaledp)
        self.lang_embed = torch.zeros(1)
        if "clip" in self.backbone:
            self.image_processor = CLIPImageProcessor.from_pretrained(self.backbone)
        elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone:
            with open(os.path.join(self.backbone, "data_config.json"), 'r', encoding='utf-8') as f:
                data_config = json.load(f)
            self.image_processor = timm.data.create_transform(**data_config, is_training=False)
        else:
            raise NotImplementedError

        self.transformations = None
        self.__getitem__(0)

    def __len__(self):
        return self.cumulative_len[-1]

    def _locate_transition(self, index):
        assert index < self.cumulative_len[-1]
        episode_index = np.argmax(self.cumulative_len > index)  # argmax returns first True index
        start_ts = index - (self.cumulative_len[episode_index] - self.episode_len[episode_index])
        episode_id = self.episode_ids[episode_index]
        return episode_id, start_ts

    def __getitem__(self, index):
        index = index % self.cumulative_len[-1]  # Ensure index wraps around
        episode_id, start_ts = self._locate_transition(index)
        dataset_path = self.dataset_path_list[episode_id]
        with h5py.File(dataset_path, 'r', libver='latest', swmr=True) as root:
            ### read lang embedding
            if 'sub_language_distilbert' in root:
                lang_embed = root['sub_language_distilbert'][()][start_ts]
                lang_embed = torch.from_numpy(lang_embed).float().squeeze()
            elif 'sub_reason_distilbert' in root: # sub_reason shape (1,1,x)
                # print(f"sub_reason_distilbert shape: {root['sub_reason_distilbert'][()].shape}")
                lang_embed = root['sub_reason_distilbert'][()][start_ts]
                lang_embed = torch.from_numpy(lang_embed).float().squeeze()
            elif 'language_distilbert' in root:
                lang_embed = root['language_distilbert'][:]
                lang_embed = torch.from_numpy(lang_embed).float().squeeze()
            else:
                # print(">>>>>>>>>>>>>>>>No language_distilbert in hdf5 file>>>>>>>>>>>>>>>>>>>")
                lang_embed = self.lang_embed
            compressed = root.attrs.get('compress', False)

            ### read image and do forward preprocess for it
            all_cam_images = []
            for cam_name in self.camera_names:
                image = root[f'/observations/images/{cam_name}'][start_ts]
                if compressed:
                    image = cv2.imdecode(image, 1)
                if image.shape[0] != 3:
                    image = np.transpose(image, (2, 0, 1))
                image = torch.from_numpy(image)
                if self.transformations is None:
                    print('Initializing transformations')
                    original_size = image.shape[1:]
                    ratio = 0.95
                    self.transformations = [
                        transforms.RandomCrop(size=[int(original_size[0] * ratio), int(original_size[1] * ratio)]),
                        transforms.Resize(original_size, antialias=True),
                        transforms.RandomRotation(degrees=[-5.0, 5.0], expand=False),
                        transforms.ColorJitter(brightness=0.3, contrast=0.4, saturation=0.5)  # , hue=0.08)
                    ]
                for transform in self.transformations:
                    image = transform(image)
                all_cam_images.append(image)

            ### read action and state, do some process function
            try:   # only used for agelix and franka
                qpos = root['/observations/qpos'][start_ts]
                action = root['/action'][()][:, :]
            except: # for mobile aloha
                if not root.get('/state/base_vel', None):
                    qpos = np.concatenate([
                        root['/state/joint_position/left'][()][:-1],
                        root['/state/joint_position/right'][()][:-1],
                    ],
                        axis=1)[start_ts]
                    action = np.concatenate([
                        root['/state/joint_position/left'][()][1:],
                        root['/state/joint_position/right'][()][1:],
                    ],
                        axis=1)
                else:
                    qpos = np.concatenate([
                        root['/state/joint_position/left'][()][:-1],
                        root['/state/joint_position/right'][()][:-1],
                        root['/state/base_vel'][()][:-1]
                    ],
                        axis=1)[start_ts]
                    action = np.concatenate([
                        root['/state/joint_position/left'][()][1:],
                        root['/state/joint_position/right'][()][1:],
                        root['/action/base_vel'][()][:-1]
                    ],
                        axis=1)
            original_action_shape = action.shape
            episode_len = original_action_shape[0]
            action = action[start_ts:]  # hack, to make timesteps more aligned
            action_len = episode_len - start_ts  # hack, to make timesteps more aligned

        root.close()
        padded_action = np.zeros((self.max_episode_len, original_action_shape[1]), dtype=np.float32)
        padded_action[:action_len] = action
        is_pad = np.zeros(self.max_episode_len)
        is_pad[action_len:] = 1
        padded_action = padded_action[:self.chunk_size]
        is_pad = is_pad[:self.chunk_size]

        # construct observations
        image_data = torch.stack(all_cam_images, dim=0)
        image_data = resize_with_pad(image_data, 224, 224, 0)
        if "clip" in self.backbone:
            image_data = self.image_processor.preprocess(image_data, return_tensors='pt')['pixel_values'][0]
        elif "vit_base_patch16_rope_reg1_gap_256" in self.backbone:
            image_data = self.image_processor(image_data.float())

        qpos_data = torch.from_numpy(qpos).float()
        action_data = torch.from_numpy(padded_action).float()
        is_pad = torch.from_numpy(is_pad).bool()

        if self.policy_class.find('Diffusion') >= 0 or self.policy_class.find('DiT') >= 0 or self.policy_class.find(
                "Droid") >= 0 or self.policy_class.find('Transformer') >= 0:
            # normalize to [-1, 1]
            action_data = ((action_data - self.norm_stats["action_min"]) / (
                    self.norm_stats["action_max"] - self.norm_stats["action_min"])) * 2 - 1
        else:
            # normalize to mean 0 std 1
            raise NotImplementedError

        qpos_data = (qpos_data - self.norm_stats["qpos_mean"]) / self.norm_stats["qpos_std"]
        return image_data, qpos_data, action_data, is_pad, lang_embed


class DistributedBatchSampler(torch.utils.data.Sampler):
    def __init__(self, dataset, batch_size, episode_len_l, sample_weights, num_replicas=None, rank=None, shuffle=True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.episode_len_l = episode_len_l
        self.sample_weights = np.array(sample_weights) / np.sum(sample_weights) if sample_weights is not None else None
        self.num_replicas = num_replicas
        self.rank = rank
        self.shuffle = shuffle

        self.sample_probs = np.array(sample_weights) / np.sum(sample_weights) if sample_weights is not None else None
        self.sum_dataset_len_l = np.cumsum([0] + [np.sum(episode_len) for episode_len in episode_len_l])

        self.dist_sampler = DistributedSampler(self.dataset, num_replicas=self.num_replicas, rank=self.rank,
                                               shuffle=self.shuffle)

    """
    def __iter__(self):
        sum_dataset_len_l = np.cumsum([0] + [np.sum(episode_len) for episode_len in self.episode_len_l])
        episode_indices = np.arange(len(self.episode_len_l))
        for _ in self.dist_sampler:
            batch = []
            attempts = 0
            while len(batch) < self.batch_size:
                episode_idx = np.random.choice(episode_indices, p=self.sample_weights)
                if self.episode_len_l[episode_idx] > 0:  # Ensure the episode has data
                    step_idx = np.random.randint(sum_dataset_len_l[episode_idx], sum_dataset_len_l[episode_idx + 1])
                    batch.append(step_idx)
                attempts += 1
                if attempts > 100 * self.batch_size:  # Safeguard against infinite loop
                    raise RuntimeError("Unable to generate a valid batch after many attempts")
            yield batch
    """

    def __iter__(self):
        batch = []
        for idx in self.dist_sampler:
            # Assume episode index selection and within-episode index calculation is done here
            # For simplicity, we're just incrementing idx to mimic selecting indices
            batch.append(idx)
            if len(batch) == self.batch_size:
                yield batch
                batch = []
        if batch:
            yield batch  # Yield the last batch if it's not empty

    def __len__(self):
        return len(self.dist_sampler) // self.batch_size


def flatten_list(l):
    return [item for sublist in l for item in sublist]

def get_norm_stats(dataset_path_list,args):
    all_qpos_data = []
    all_action_data = []
    all_episode_len = []

    for dataset_path in dataset_path_list:
        try:
            with h5py.File(dataset_path, 'r') as root:
                try:  # only used for agelix and franka
                    qpos = root['/observations/qpos'][()]
                    action = root['/action'][()][:]
                except:  # for mobile aloha
                    if not root.get('/state/base_vel', None):
                        qpos = np.concatenate([
                            root['/state/joint_position/left'][()][:-1],
                            root['/state/joint_position/right'][()][:-1],
                        ], axis=1)
                        action = np.concatenate([
                            root['/state/joint_position/left'][()][1:],
                            root['/state/joint_position/right'][()][1:],
                        ], axis=1)
                    else:
                        qpos = np.concatenate([
                            root['/state/joint_position/left'][()][:-1],
                            root['/state/joint_position/right'][()][:-1],
                            root['/state/base_vel'][()][:-1]
                        ], axis=1)
                        action = np.concatenate([
                            root['/state/joint_position/left'][()][1:],
                            root['/state/joint_position/right'][()][1:],
                            root['/action/base_vel'][()][:-1]
                        ], axis=1)
        except Exception as e:
            print(f'Error loading {dataset_path} in get_norm_stats')
            print(e)
            quit()
        all_qpos_data.append(torch.from_numpy(qpos))
        all_action_data.append(torch.from_numpy(action))
        all_episode_len.append(len(qpos))

    all_qpos_data = torch.cat(all_qpos_data, dim=0)
    all_action_data = torch.cat(all_action_data, dim=0)

    # normalize action data
    action_mean = all_action_data.mean(dim=[0]).float()
    action_std = all_action_data.std(dim=[0]).float()
    action_std = torch.clip(action_std, 1e-2, np.inf)  # clipping

    # normalize qpos data
    qpos_mean = all_qpos_data.mean(dim=[0]).float()
    qpos_std = all_qpos_data.std(dim=[0]).float()
    qpos_std = torch.clip(qpos_std, 1e-2, np.inf)  # clipping

    action_min = all_action_data.min(dim=0).values.float()
    action_max = all_action_data.max(dim=0).values.float()
    del all_action_data, all_qpos_data
    eps = 0.0001
    stats = {"action_mean": action_mean.numpy(), "action_std": action_std.numpy(),
             "action_min": action_min.numpy() - eps, "action_max": action_max.numpy() + eps,
             "qpos_mean": qpos_mean.numpy(), "qpos_std": qpos_std.numpy(),
             "example_qpos": qpos}

    return stats, all_episode_len


def find_all_hdf5(dataset_dir):
    hdf5_files = []
    for root, dirs, files in os.walk(dataset_dir):

        for filename in fnmatch.filter(files, '*.hdf5'):
            if 'features' in filename: continue
            if 'pointclouds' in filename: continue
            hdf5_files.append(os.path.join(root, filename))
    print(f'Found {len(hdf5_files)} hdf5 files')
    return hdf5_files


def BatchSampler(batch_size, episode_len_l, sample_weights):
    sample_probs = np.array(sample_weights) / np.sum(sample_weights) if sample_weights is not None else None
    sum_dataset_len_l = np.cumsum([0] + [np.sum(episode_len) for episode_len in episode_len_l])
    while True:
        batch = []
        for _ in range(batch_size):
            episode_idx = np.random.choice(len(episode_len_l), p=sample_probs)
            step_idx = np.random.randint(sum_dataset_len_l[episode_idx], sum_dataset_len_l[episode_idx + 1])
            batch.append(step_idx)
        yield batch


def load_data(dataset_dir_l, camera_names, batch_size_train, chunk_size,
              skip_mirrored_data=False, stats_dir_l=None, sample_weights=None,
              args=None, rank=None):
    with open(os.path.join(args["ckpt_dir"], "dataset_stats.pkl"), "rb") as f:
        norm_stats = pickle.load(f)
    
    dataset_path_list = norm_stats["dataset_path_list"]
    train_episode_ids = np.arange(len(dataset_path_list))
    train_episode_len = norm_stats["all_episode_len"]
    if rank is not None and rank == 0:
        print(f'Data from: {dataset_dir_l}\n- Train on total of {len(dataset_path_list)} episodes')
    else:
        print(f'Data from: {dataset_dir_l}\n- Train on total of {len(dataset_path_list)} episodes')

    if torch.cuda.device_count == 1:
        batch_sampler_train = BatchSampler(batch_size_train, train_episode_len_l, sample_weights)
        # construct dataset and dataloader
        train_dataset = EpisodicDataset(dataset_path_list, camera_names, norm_stats, train_episode_ids,
                                        train_episode_len, chunk_size,  args=args, rank=rank)
        train_num_workers = 16  # 16
        # pin_memory=True
        train_dataloader = DataLoader(train_dataset, batch_sampler=batch_sampler_train, pin_memory=True,
                                      num_workers=train_num_workers, prefetch_factor=2)
    else:
        train_dataset = EpisodicDataset(dataset_path_list, camera_names, norm_stats, train_episode_ids,
                                        train_episode_len, chunk_size, args=args)
        num_replicas = torch.cuda.device_count()
        train_num_workers = 16  # 2
        batch_sampler_train = DistributedBatchSampler(train_dataset, batch_size_train, train_episode_len,
                                                      sample_weights, num_replicas=num_replicas, rank=rank)
        # prefetch_factor=2
        train_dataloader = DataLoader(
            train_dataset,
            batch_sampler=batch_sampler_train, pin_memory=True, num_workers=train_num_workers)
    
    return train_dataloader, norm_stats

### helper functions
def set_seed(seed):
    torch.manual_seed(seed)
    np.random.seed(seed)