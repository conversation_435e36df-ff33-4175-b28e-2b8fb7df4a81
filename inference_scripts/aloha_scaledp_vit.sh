#!/bin/bash
backbone='/home/<USER>/Documents/zhumj/model_param/mllm/timm/vit_base_patch16_rope_reg1_gap_256.sbb_in1k'
task_name=folding_two_shirts_by_drag  #mobile_franka_bin_picking_compressed
chunk_size=50
batch_size=40
backbone_class="vit_rope"
policy_class=DroidDiT
model_size=RDiT-Gemma
lr=1e-4

lr_backbone=1e-5
lang=wo

seed=0
double_arm=1
num_steps=100000
save_every=10000
use_constant=0
eta_min=1e-5
ckpt_dir=/media/eai/Elements/robotics/model_Param/mobile_franka_param/scaledp/vit/${task_name}_${model_size}_vit_base_${backbone_class}_lr_backbone_${lr_backbone}_chunk_${chunk_size}_${lang}_lang_bs_${batch_size}_constant_${use_constant}_eta_min_${eta_min}
python3 evaluate/eval_aloha_gemma_fast.py \
--task_name $task_name \
--ckpt_dir $ckpt_dir \
--policy_class $policy_class --chunk_size $chunk_size --batch_size $batch_size \
--backbone $backbone --model_size ${model_size} \
--num_steps $num_steps --save_every $save_every  --lr $lr  --lr_backbone $lr_backbone \
--double_arm $double_arm \
--seed $seed --raw_lang "The crumpled shirts are in the basket. Pick it out and fold it." \
--ckpt_name 'policy_step_50000_compressed'