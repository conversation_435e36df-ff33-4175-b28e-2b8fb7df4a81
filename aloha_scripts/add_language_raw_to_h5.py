import os.path
import h5py
import os
import torch
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModel

tokenizer = AutoTokenizer.from_pretrained('/home/<USER>/tzb/zhumj/model_param/distilbert-base-uncased/')
model = AutoModel.from_pretrained("/home/<USER>/tzb/zhumj/model_param/distilbert-base-uncased/",
                                  torch_dtype=torch.float16)
model.to('cuda')

def add_language_to_hdf5(file_path, raw_lang, encoded_lang):
    with h5py.File(file_path, 'a') as hdf5_file:
        if "language_raw" not in hdf5_file.keys():
            hdf5_file.create_dataset("language_raw", data=[raw_lang])
        else:
            hdf5_file.attrs['language_raw'] = hdf5_file['language_raw'][()]
            hdf5_file['language_raw'][()] = [raw_lang]
        if "language_distilbert" not in hdf5_file.keys() and encoded_lang is not None:
            hdf5_file.create_dataset("language_distilbert", data=encoded_lang.cpu().detach().numpy())


if __name__=="__main__":
    raw_lang="Put the clothes on the sofa into the basket."
    tasks = [
        "0507_pick_cloth_from_sofa_Leo_compressed",
        "05_06_pick_cloth_from_sofa_neo_compressed",
        "05_03_pick_cloth_from_sofa_wjj_compressed",
        "05_08_pick_cloth_from_sofa_Leo_compressed"
    ]
    DATA_DIR = '/home/<USER>/tzb/h5py_data/mobile_aloha/'
    for task in tasks:
        file_path = os.path.join(DATA_DIR, task)
        file_list = os.listdir(file_path)
        file_list = [f for f in file_list if f.endswith('.hdf5')]

        # encode lang
        encoded_input = tokenizer(raw_lang, return_tensors="pt").to("cuda")
        outputs = model(**encoded_input)
        encoded_lang = outputs.last_hidden_state.sum(1).squeeze().unsqueeze(0)

        for file_name in tqdm(file_list[:]):
            print(file_path, file_name)
            add_language_to_hdf5(file_path=os.path.join(file_path, file_name), raw_lang=raw_lang, encoded_lang=encoded_lang)