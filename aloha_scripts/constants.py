### Task parameters

# DATA_DIR = '<put your data dir here>'
# TASK_CONFIGS = {
#     'aloha_wear_shoe':{
#         'dataset_dir': '/aloha_wear_shoe',
#         'num_episodes': 50,
#         'episode_len': 1000,
#         'camera_names': ['cam_high', 'cam_low', 'cam_left_wrist', 'cam_right_wrist']
#     },
# }

REMOTE_DATA_DIR="/home/<USER>/tzb/h5py_data/"
LOCAL_DATA_DIR = '/home/<USER>/zhumj/data'
Hand_Data_dir = "/home/<USER>/tzb/h5py_data/franka_hand/"

print("")
TASK_CONFIGS = {
    'folding_data_0609': {
        'dataset_dir': [
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250530_random_fold_stacked_T-shirts_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250603_random_fold_stacked_T-shirts_zby_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_3_wheels/20250603_random_fold_stacked_T-shirts_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250521_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250522_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250523_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250526_fold_pants_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250526_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250527_fold_pants_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250527_fold_pants_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250528_fold_T-shirts_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250529_fold_T-shirts_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/mobile_aloha_4_wheels/20250529_fold_T-shirts_zby_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250526_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250527_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_zjm_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250528_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_Leo_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_zjm_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250529_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250530_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250603_random_folding_pants_lyp_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/20250603_random_folding_pants_zjm_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_Leo_20250522_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_zjm_20250522_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/folding_shirts_stack_zjm_20250523_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_Leo_20250526_noon_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250526_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250526_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250527_2_compressed",
            "/data/efs/qiaoyi/EAI_robot_data/static_aloha/random_folding_pants_zjm_20250527_compressed"
        ],
        'episode_len': 1000,
        'camera_names': ['cam_high', 'cam_left_wrist', 'cam_right_wrist']
    },
    "new_folding_pants_with_flatten_state":{
        'dataset_dir': [
                "/home/<USER>/tzb/h5py_data/aloha_compressed_70/new_folding_pants_leo_20250422/",
                "/home/<USER>/tzb/h5py_data/aloha_compressed_70/new_folding_pants_leo_20250423/",
                "/home/<USER>/tzb/h5py_data/aloha_compressed_70/new_folding_pants_leo_20250424/"
            ],
        'episode_len': 2000,  # 1000,
        'camera_names': ['cam_high', 'cam_left_wrist', 'cam_right_wrist']
    },
    'aloah_3_cameras_all_data_1_17_folding_only': {
        'dataset_dir': [
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_lxy1213',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_lxy1214',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_zmj1212',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_zmj1213',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_zzy1213',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_junjie_1224',  # 50
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_zhongyi_1224',  # 42
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_wjj1213_meeting_room',  # 42
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_30_12_31_extract/folding_shirt_12_30_12_31/folding_shirt_12_30_wjj_weiqing_recover',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_30_12_31_extract/folding_shirt_12_30_12_31/folding_shirt_12_31_wjj_lab_marble_recover',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_30_12_31_extract/folding_shirt_12_30_12_31/folding_shirt_12_31_zhouzy_lab_marble',
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_blue_tshirt_yichen_0103",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_blue_tshirt_xiaoyu_0103",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_blue_tshirt_yichen_0102",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_28_zzy_right_first",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_27_office",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/0107_wjj_folding_blue_shirt",
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_second_tshirt_yichen_0108',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_second_tshirt_wjj_0108',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_random_yichen_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_random_table_right_wjj_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_two_tshirt_yichen_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_second_tshirt_yichen_0110',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_second_tshirt_yichen_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_second_tshirt_wjj_0110',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/data_01_11_13_7z_exact/data_01_11_13/folding_basket_second_tshirt_yichen_0111',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/data_01_11_13_7z_exact/data_01_11_13/folding_basket_second_tshirt_wjj_0113',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/data_01_11_13_7z_exact/data_01_11_13/folding_basket_second_tshirt_wjj_0111',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/1_14_data_move_add_folding_shirt/move_data/folding_basket_second_tshirt_yichen_0114',
            # 1.17 2025 new add
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_first_tshirt_dark_blue_yichen_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_first_tshirt_pink_wjj_0115",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_blue_yichen_0115",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_dark_blue_yichen_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_red_lxy_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_red_wjj_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_shu_red_yellow_wjj_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_yellow_shu_red_wjj_0116"
        ],
        'episode_len': 1000,  # 1000,
        # 'camera_names': ['cam_high', 'cam_low', 'cam_left_wrist', 'cam_right_wrist']
        'camera_names': ['cam_high', 'cam_left_wrist', 'cam_right_wrist']
    },
    'aloah_3_cameras_all_data_1_17': {
        'dataset_dir': [
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_lxy1213',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_lxy1214',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_zmj1212',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_zmj1213',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_zzy1213',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_junjie_1224',  # 50
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_zhongyi_1224',  # 42
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/fold_shirt_wjj1213_meeting_room',  # 42
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_30_12_31_extract/folding_shirt_12_30_12_31/folding_shirt_12_30_wjj_weiqing_recover',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_30_12_31_extract/folding_shirt_12_30_12_31/folding_shirt_12_31_wjj_lab_marble_recover',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_30_12_31_extract/folding_shirt_12_30_12_31/folding_shirt_12_31_zhouzy_lab_marble',
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_blue_tshirt_yichen_0103",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_blue_tshirt_xiaoyu_0103",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_blue_tshirt_yichen_0102",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_28_zzy_right_first",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/folding_shirt_12_27_office",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/0107_wjj_folding_blue_shirt",
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_second_tshirt_yichen_0108',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_second_tshirt_wjj_0108',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_random_yichen_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_random_table_right_wjj_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_two_tshirt_yichen_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_second_tshirt_yichen_0110',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_second_tshirt_yichen_0109',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_10_extract/folding_basket_second_tshirt_wjj_0110',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/data_01_11_13_7z_exact/data_01_11_13/folding_basket_second_tshirt_yichen_0111',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/data_01_11_13_7z_exact/data_01_11_13/folding_basket_second_tshirt_wjj_0113',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/data_01_11_13_7z_exact/data_01_11_13/folding_basket_second_tshirt_wjj_0111',
            '/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/1_14_data_move_add_folding_shirt/move_data/folding_basket_second_tshirt_yichen_0114',
            # 1.17 2025 new add
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_first_tshirt_dark_blue_yichen_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_first_tshirt_pink_wjj_0115",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_blue_yichen_0115",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_dark_blue_yichen_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_red_lxy_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_red_wjj_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_shu_red_yellow_wjj_0116",
            "/home/<USER>/tzb/h5py_data//aloha_bimanual/aloha_4views/7z_1_15_16_data_extract/weiqing_folding_basket_second_tshirt_yellow_shu_red_wjj_0116",

            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_ljm_1217',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_zmj_1217_green_plate_coke_can_brown_mug_bottle',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_lxy_1220_blue_plate_pink_paper_cup_plastic_bag_knife',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_zzy_1220_green_paper_cup_wulong_bottle_pink_bowl_brown_spoon',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_zmj_1220_green_cup_blue_paper_ball_pink_plate_sprite',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_zmj_1217_green_plate_coke_can_brown_mug_bottle',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/clean_table_lxy_1222_pick_place_water_left_arm',

            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/pick_cup_and_pour_water_wjj_weiqing_coke',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/pick_cars_from_moving_belt_waibao_1227',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/pick_cup_and_pour_water_wjj_weiqing_coffee',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/pick_cars_from_moving_belt_zhumj_1227',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/hang_cups_waibao',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/storage_bottle_green_tea_oolong_mineral_water_ljm_weiqing_1225_right_hand',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/aloha_data/storage_bottle_green_tea_oolong_mineral_water_lxy_weiqing_1225',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/get_papercup_yichen_1223',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/pour_coffee_zhaopeiting_1224',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/get_papercup_and_pour_coke_yichen_1224',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/pick_up_coke_in_refrigerator_yichen_1223',
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/pour_rice_yichen_0102',

            # from Shanghai University
            '/home/<USER>/tzb/h5py_data/aloha_bimanual/aloha_4views/pick_paper_ball_from_bike',
        ],
        'episode_len': 1000,  # 1000,
        # 'camera_names': ['cam_high', 'cam_low', 'cam_left_wrist', 'cam_right_wrist']
        'camera_names': ['cam_high', 'cam_left_wrist', 'cam_right_wrist']
    },
    'mobile_franka_bin_picking_compressed': {
        'dataset_dir': [
            REMOTE_DATA_DIR + '/mobile_franka_data_compressed/0102_green_paper_cup_yellow_bus_hex_key_gloves_480_640/0102_green_paper_cup_yellow_bus_hex_key_gloves_480_640_succ_t0001_s-0-0',
            REMOTE_DATA_DIR + '/mobile_franka_data_compressed/0102_toy_blue_van_pear_tape_480_640/0102_toy_blue_van_pear_tape_480_640_succ_t0001_s-0-0',
            REMOTE_DATA_DIR + '/mobile_franka_data_compressed/0103_brown_mug_cutter_knife_bread_banana_480_640/0103_brown_mug_cutter_knife_bread_banana_480_640_succ_t0001_s-0-0',
            REMOTE_DATA_DIR + '/mobile_franka_data_compressed/0103_green_can_tennis_ball_sponge_brown_plate_480_640/0103_green_can_tennis_ball_sponge_brown_plate_480_640_succ_t0001_s-0-0',
            REMOTE_DATA_DIR + '/mobile_franka_data_compressed/0103_pink_penguin_lemon_cyan_trunk_gray_shovel_480_640/0103_pink_penguin_lemon_cyan_trunk_gray_shovel_480_640_succ_t0001_s-0-0',
            REMOTE_DATA_DIR + '/mobile_franka_data_compressed/0103_rubik_cube_apple_pink_cube_whiteboard_marker_480_640/0103_rubik_cube_apple_pink_cube_whiteboard_marker_480_640_succ_t0001_s-0-0',
        ],
        'episode_len': 1000,  # 1000,
        'camera_names': ['left', 'right', 'wrist']
    },
    'franka_bin_picking': {
        'dataset_dir': [
            "0102_green_paper_cup_yellow_bus_hex_key_gloves_480_640",
            "0102_toy_blue_van_pear_tape_480_640",
            "0103_brown_mug_cutter_knife_bread_banana_480_640",
            "0103_green_can_tennis_ball_sponge_brown_plate_480_640",
            "0103_pink_penguin_lemon_cyan_trunk_gray_shovel_480_640",
            "0103_rubik_cube_apple_pink_cube_whiteboard_marker_480_640",
            "0104_rubik_cube_cyan_trunk_tape_hex_key_480_640",
            "0105_apple_pear_lemon_tennis_ball_480_640",
            "0105_brown_mug_toy_tennis_ball_sponge_480_640",
            "0105_pink_penguin_shovel_bananan_golves_480_640",
            "0105_green_paper_cup_cutter_knife_whiteboard_marker_brown_plate_480_640",
        ],
        # 'sample_weights': [1],
        'episode_len': 2000,  # 1000,
        'camera_names': ["left", 'right', "wrist"]
    },
    "local_debug": {
        'dataset_dir': [
            LOCAL_DATA_DIR + '/franka/4_types_pikachu_blue_van_hex_key_glove_480_640',
            LOCAL_DATA_DIR + '/franka/t2',
        ],
        'episode_len': 1000,  # 1000,
        'camera_names': ['left', 'right', 'wrist'],
        "sample_weights": [1, 1]
    },
}

LEROBOT_CONFIG = {
    'aloha_folding_shirt_lerobot_3_26': {
            'dataset_dir': [
                'fold_shirt_lxy1213', 'fold_shirt_lxy1214', 'fold_shirt_zmj1212', 'fold_shirt_zmj1213',
                'fold_shirt_zzy1213', 'folding_junjie_1224', 'folding_zhongyi_1224', 'fold_shirt_wjj1213_meeting_room',
                'folding_shirt_12_30_wjj_weiqing_recover', 'folding_shirt_12_31_wjj_lab_marble_recover',
                'folding_shirt_12_31_zhouzy_lab_marble', "folding_blue_tshirt_yichen_0103", "folding_blue_tshirt_xiaoyu_0103",
                "folding_blue_tshirt_yichen_0102", "folding_shirt_12_28_zzy_right_first", "folding_shirt_12_27_office",
                "0107_wjj_folding_blue_shirt", 'folding_second_tshirt_yichen_0108', 'folding_second_tshirt_wjj_0108',
                'folding_random_yichen_0109', 'folding_random_table_right_wjj_0109', 'folding_basket_two_tshirt_yichen_0109',
                'folding_basket_second_tshirt_yichen_0110', 'folding_basket_second_tshirt_yichen_0109',
                'folding_basket_second_tshirt_wjj_0110', 'folding_basket_second_tshirt_yichen_0111',
                'folding_basket_second_tshirt_wjj_0113', 'folding_basket_second_tshirt_wjj_0111', 'folding_basket_second_tshirt_yichen_0114',
                # 1.17 2025 new add
                "weiqing_folding_basket_first_tshirt_dark_blue_yichen_0116",
                "weiqing_folding_basket_first_tshirt_pink_wjj_0115",
                # "weiqing_folding_basket_second_tshirt_blue_yichen_0115",
                "weiqing_folding_basket_second_tshirt_dark_blue_yichen_0116",
                "weiqing_folding_basket_second_tshirt_red_lxy_0116",
                "weiqing_folding_basket_second_tshirt_red_wjj_0116",
                "weiqing_folding_basket_second_tshirt_shu_red_yellow_wjj_0116",
                "weiqing_folding_basket_second_tshirt_yellow_shu_red_wjj_0116",
                # 1.21 added
                "unloading_dryer_yichen_0120",
                "unloading_dryer_yichen_0119",
                # 1.22
                "folding_random_short_first_wjj_0121",
                "folding_random_short_second_wjj_0121",
                # 1.23
                "folding_random_short_second_wjj_0122",
                "folding_random_short_first_wjj_0122",
                # 1.25
                "folding_random_tshirt_first_wjj_0124",
                "folding_random_tshirt_second_wjj_0124",
                # 3.26
                "fold_two_shirts_zmj_03_26_lerobot",
                "fold_two_shirts_zmj_03_21_lerobot",
                "fold_two_shirts_wjj_03_21",
                "fold_two_shirts_zmj_03_24_lerobot"
            ],
            # 'sample_weights': [1],
            'episode_len': 2000,  # 1000,
            'camera_names': ['observation.images.cam_high', "observation.images.cam_left_wrist",
                             "observation.images.cam_right_wrist"]
        },
    "folding_two_shirts_by_drag": {
        'dataset_dir': [
            "fold_two_shirts_zmj_03_26_lerobot",
            "fold_two_shirts_zmj_03_21_lerobot",
            "fold_two_shirts_wjj_03_21",
            "fold_two_shirts_zmj_03_24_lerobot"
        ],
        # 'sample_weights': [1],
        'episode_len': 2000,  # 1000,
        'camera_names': ['observation.images.cam_high', "observation.images.cam_left_wrist",
                         "observation.images.cam_right_wrist"]
    },
    "local_debug_lerobot": {
        'dataset_dir': [
            'clean_table_2_22',
        ],
        'episode_len': 2000,  # 1000,
        'camera_names': ["observation.images.cam_front", 'observation.images.cam_high']
    },
}
for k,v in LEROBOT_CONFIG.items():
    TASK_CONFIGS[k] = v

MOBILE_ALOHA_CONFIG = {
    'mobile_aloha_pick_cloth_from_sofa': {
            'dataset_dir': [
                "/home/<USER>/tzb/h5py_data/mobile_aloha/0507_pick_cloth_from_sofa_Leo_compressed/",
                "/home/<USER>/tzb/h5py_data/mobile_aloha/05_06_pick_cloth_from_sofa_neo_compressed/",
                "/home/<USER>/tzb/h5py_data/mobile_aloha/05_03_pick_cloth_from_sofa_wjj_compressed/",
                "/home/<USER>/tzb/h5py_data/mobile_aloha/05_08_pick_cloth_from_sofa_Leo_compressed/"
            ],
            # 'sample_weights': [1],
            'episode_len': 2000,  # 1000,
            'camera_names': ['cam_high', "cam_left_wrist", "cam_right_wrist"]
        },
    'mobile_aloha_table_bussing_chenyf': {
        'dataset_dir': [
            "/home/<USER>/tzb/h5py_data/aloha_quest3_data/cyf_clean_table/compressed/blue_cup_beige_bowl_Costa_coffee_green_paper_cup",
            "/home/<USER>/tzb/h5py_data/aloha_quest3_data/cyf_clean_table/compressed/white_soup_spoon_green_bowl_orange_paper_cup_nescafe_coffee",
        ],
        'episode_len': 2000,  # 1000,
        'camera_names': ['cam_high', "cam_left_wrist", "cam_right_wrist"]
    },
    'mobile_aloha_0505': {
        'dataset_dir': [
            "/home/<USER>/tzb/h5py_data/mobile_aloha/0507_pick_cloth_from_sofa_Leo_compressed/",
            "/home/<USER>/tzb/h5py_data/mobile_aloha/05_06_pick_cloth_from_sofa_neo_compressed/",
            "/home/<USER>/tzb/h5py_data/mobile_aloha/05_03_pick_cloth_from_sofa_wjj_compressed/",
            "/home/<USER>/tzb/h5py_data/aloha_quest3_data/cyf_clean_table/new_compressed/beige_bowl_blue_cup_green_paper_cup_Costa_coffee/",
            "/home/<USER>/tzb/h5py_data/aloha_quest3_data/cyf_clean_table/new_compressed/green_bowl_white_soup_spoon_orange_paper_cup_nescafe_coffee/"
        ],
        # 'sample_weights': [1],
        'episode_len': 2000,  # 1000,
        'camera_names': ['cam_high', "cam_left_wrist", "cam_right_wrist"]
    }
}

for k,v in MOBILE_ALOHA_CONFIG.items():
    TASK_CONFIGS[k] = v

XHand_CONFIG = {
    "screwing_05_30":{
        'dataset_dir': [
            # 2025-05-30
            Hand_Data_dir + '2025-05-30_lyp/3row_4col_05_30_lyp_480_640/3row_4col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/3row_3col_05_30_lyp_480_640/3row_3col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/3row_2col_05_30_lyp_480_640/3row_2col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/3row_1col_05_30_lyp_480_640/3row_1col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/2row_4col_05_30_lyp_480_640/2row_4col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/2row_3col_05_30_lyp_480_640/2row_3col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/2row_2col_05_30_lyp_480_640/2row_2col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/2row_1col_05_30_lyp_480_640/2row_1col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/1row_4col_05_30_lyp_480_640/1row_4col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/1row_3col_05_30_lyp_480_640/1row_3col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/1row_2col_05_30_lyp_480_640/1row_2col_05_30_lyp_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-30_lyp/1row_1col_05_30_lyp_480_640/1row_1col_05_30_lyp_480_640_succ_t0001_s-0-0',

            # 2025-05-29
            Hand_Data_dir + '2025-05-29_lyp/2row_1col_480_640/2row_1col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/1row_4col_480_640/1row_4col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/1row_3col_480_640/1row_3col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/1row_2col_480_640/1row_2col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/1row_1col_480_640/1row_1col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/3row_4col_480_640/3row_4col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/3row_3col_480_640/3row_3col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/3row_2col_480_640/3row_2col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/3row_1col_480_640/3row_1col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/2row_4col_480_640/2row_4col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/2row_3col_480_640/2row_3col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-29_lyp/2row_2col_480_640/2row_2col_480_640_succ_t0001_s-0-0',

            # 2025-05-28
            Hand_Data_dir + '2025-05-28_lyp/3row_4col_480_640/3row_4col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-28_lyp/3row_3col_480_640/3row_3col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-28_lyp/3row_2col_480_640/3row_2col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-28_lyp/3row_1col_480_640/3row_1col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-28_lyp/2row_4col_480_640/2row_4col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-28_lyp/2row_3col_480_640/2row_3col_480_640_succ_t0001_s-0-0',
            Hand_Data_dir + '2025-05-28_lyp/2row_2col_480_640/2row_2col_480_640_succ_t0001_s-0-0',

            # 2025-05-27
            Hand_Data_dir + '2025-05-27_lyp/2row_1col_480_640_succ_t0001_s-0-0_compressed',
            Hand_Data_dir + '2025-05-27_lyp/1row_4col_480_640_succ_t0001_s-0-0_compressed',
            Hand_Data_dir + '2025-05-27_lyp/1row_3col_480_640_succ_t0001_s-0-0_compressed',
            Hand_Data_dir + '2025-05-27_lyp/1row_2col_13hz_480_640_succ_t0001_s-0-0_compressed',
            Hand_Data_dir + '2025-05-27_lyp/1row_1col_480_640_succ_t0001_s-0-0_compressed',
            ],
        'episode_len': 2000,  # 1000,
        'camera_names': ['left', 'front', 'right']
    },
}

for k,v in XHand_CONFIG.items():
    TASK_CONFIGS[k] = v

### ALOHA fixed constants
DT = 0.02
JOINT_NAMES = ["waist", "shoulder", "elbow", "forearm_roll", "wrist_angle", "wrist_rotate"]
START_ARM_POSE = [0, -0.96, 1.16, 0, -0.3, 0, 0.02239, -0.02239,  0, -0.96, 1.16, 0, -0.3, 0, 0.02239, -0.02239]

# Left finger position limits (qpos[7]), right_finger = -1 * left_finger
MASTER_GRIPPER_POSITION_OPEN = 0.02417
MASTER_GRIPPER_POSITION_CLOSE = 0.01244
PUPPET_GRIPPER_POSITION_OPEN = 0.05800
PUPPET_GRIPPER_POSITION_CLOSE = 0.01844

# Gripper joint limits (qpos[6])
MASTER_GRIPPER_JOINT_OPEN = 0.3083
MASTER_GRIPPER_JOINT_CLOSE = -0.6842
PUPPET_GRIPPER_JOINT_OPEN = 1.4910
PUPPET_GRIPPER_JOINT_CLOSE = -0.6213

############################ Helper functions ############################

MASTER_GRIPPER_POSITION_NORMALIZE_FN = lambda x: (x - MASTER_GRIPPER_POSITION_CLOSE) / (MASTER_GRIPPER_POSITION_OPEN - MASTER_GRIPPER_POSITION_CLOSE)
PUPPET_GRIPPER_POSITION_NORMALIZE_FN = lambda x: (x - PUPPET_GRIPPER_POSITION_CLOSE) / (PUPPET_GRIPPER_POSITION_OPEN - PUPPET_GRIPPER_POSITION_CLOSE)
MASTER_GRIPPER_POSITION_UNNORMALIZE_FN = lambda x: x * (MASTER_GRIPPER_POSITION_OPEN - MASTER_GRIPPER_POSITION_CLOSE) + MASTER_GRIPPER_POSITION_CLOSE
PUPPET_GRIPPER_POSITION_UNNORMALIZE_FN = lambda x: x * (PUPPET_GRIPPER_POSITION_OPEN - PUPPET_GRIPPER_POSITION_CLOSE) + PUPPET_GRIPPER_POSITION_CLOSE
MASTER2PUPPET_POSITION_FN = lambda x: PUPPET_GRIPPER_POSITION_UNNORMALIZE_FN(MASTER_GRIPPER_POSITION_NORMALIZE_FN(x))

MASTER_GRIPPER_JOINT_NORMALIZE_FN = lambda x: (x - MASTER_GRIPPER_JOINT_CLOSE) / (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE)
PUPPET_GRIPPER_JOINT_NORMALIZE_FN = lambda x: (x - PUPPET_GRIPPER_JOINT_CLOSE) / (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE)
MASTER_GRIPPER_JOINT_UNNORMALIZE_FN = lambda x: x * (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE) + MASTER_GRIPPER_JOINT_CLOSE
PUPPET_GRIPPER_JOINT_UNNORMALIZE_FN = lambda x: x * (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE) + PUPPET_GRIPPER_JOINT_CLOSE
MASTER2PUPPET_JOINT_FN = lambda x: PUPPET_GRIPPER_JOINT_UNNORMALIZE_FN(MASTER_GRIPPER_JOINT_NORMALIZE_FN(x))

MASTER_GRIPPER_VELOCITY_NORMALIZE_FN = lambda x: x / (MASTER_GRIPPER_POSITION_OPEN - MASTER_GRIPPER_POSITION_CLOSE)
PUPPET_GRIPPER_VELOCITY_NORMALIZE_FN = lambda x: x / (PUPPET_GRIPPER_POSITION_OPEN - PUPPET_GRIPPER_POSITION_CLOSE)

MASTER_POS2JOINT = lambda x: MASTER_GRIPPER_POSITION_NORMALIZE_FN(x) * (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE) + MASTER_GRIPPER_JOINT_CLOSE
MASTER_JOINT2POS = lambda x: MASTER_GRIPPER_POSITION_UNNORMALIZE_FN((x - MASTER_GRIPPER_JOINT_CLOSE) / (MASTER_GRIPPER_JOINT_OPEN - MASTER_GRIPPER_JOINT_CLOSE))
PUPPET_POS2JOINT = lambda x: PUPPET_GRIPPER_POSITION_NORMALIZE_FN(x) * (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE) + PUPPET_GRIPPER_JOINT_CLOSE
PUPPET_JOINT2POS = lambda x: PUPPET_GRIPPER_POSITION_UNNORMALIZE_FN((x - PUPPET_GRIPPER_JOINT_CLOSE) / (PUPPET_GRIPPER_JOINT_OPEN - PUPPET_GRIPPER_JOINT_CLOSE))

MASTER_GRIPPER_JOINT_MID = (MASTER_GRIPPER_JOINT_OPEN + MASTER_GRIPPER_JOINT_CLOSE)/2
